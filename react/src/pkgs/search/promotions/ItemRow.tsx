import { Item } from '@/pkgs/search/promotions/types'
import { CardActionArea, Grid, Typography } from '@mui/material'
import { CustomMenuItem } from '@/common/components/custom-context-menu/CustomMenu'
import { EditOutlined, Preview } from '@mui/icons-material'
import DeleteIcon from '@mui/icons-material/Delete'
import React from 'react'
import { MenuLightCell } from '@/pkgs/grid/cells/MenuLightCell'
import { usePreview } from '@/pkgs/search/promotions/usePreview'

type ItemRowProps = {
    item: Item
    index: number
    onEdit: (index: number) => void
    onDelete: (index: number) => void
}

export function ItemRow({ item, index, onEdit, onDelete }: ItemRowProps) {
    const result = usePreview(item)

    const menuItems = () => {
        return (onClose: () => void) => {
            return [
                <CustomMenuItem
                    key='edit'
                    text={'Edit'}
                    onClick={() => {
                        onEdit(index)
                        onClose()
                    }}
                >
                    <EditOutlined />
                </CustomMenuItem>,

                result.data.PreviewLink ? (
                    <CustomMenuItem
                        key={'Preview'}
                        text={'Preview'}
                        onClick={() => {
                            window.open(result.data.PreviewLink, '_blank')
                            onClose()
                        }}
                    >
                        <Preview />
                    </CustomMenuItem>
                ) : null,

                // TODO: Add Edit document later (for now users can only select content)
                result.data.EditLink ? (
                    <CustomMenuItem
                        key={'Edit Content'}
                        text={'Edit Content'}
                        onClick={() => {
                            item.ExtID && window.open(result.data.EditLink, item.ExtID)
                            onClose()
                        }}
                    >
                        <EditOutlined />
                    </CustomMenuItem>
                ) : null,

                <CustomMenuItem
                    key={'Delete'}
                    text={'Delete'}
                    onClick={() => {
                        onDelete(index)
                        onClose()
                    }}
                >
                    <DeleteIcon />
                </CustomMenuItem>
            ]
        }
    }

    return (
        <Grid container spacing={2}>
            <Grid item xs={11}>
                <CardActionArea onClick={() => onEdit(index)} sx={{ p: 1 }}>
                    <Grid container spacing={12}>
                        <Grid item xs={10}>
                            <Typography variant={'subtitle2'}>{item.Title || result?.data?.['Title']}</Typography>
                            {item.Link ? (
                                <Typography variant={'caption'}>{item.Link}</Typography>
                            ) : (
                                result.data && <Typography variant={'caption'}>{result.data.PreviewLink}</Typography>
                            )}
                            {item.Description && <Typography variant={'body1'}>{item.Description}</Typography>}
                        </Grid>
                        <Grid item xs={1}>
                            {item.Type}
                        </Grid>
                        <Grid item xs={1}>
                            {item.DisplayOrder}
                        </Grid>
                    </Grid>
                </CardActionArea>
            </Grid>
            <Grid item xs={1}>
                <MenuLightCell itemsFactory={menuItems()} />
            </Grid>
        </Grid>
    )
}
