import { useQuery } from '@tanstack/react-query'
import { LoadingSpinner } from '@/common/components/CenteredSpinner'
import { string, z } from 'zod'
import { httpPost } from '@/common/client'
import { ReservableAPI } from '@/pkgs/reservation/requests'
import { useAppContext } from '@/pkgs/auth/atoms'
import { useIDToName } from '@/pkgs/grid/cells/GridCells'
import IconButton from '@mui/material/IconButton'
import { guessErrorMessage } from '@/helpers/guessErrorMessage'
import { SyncProblem } from '@mui/icons-material'

type ReservationInfoProps = {
    key: string
    isActive: boolean
    onClick?: () => void
}

export function ReservationInfo({ key, isActive, onClick }: ReservationInfoProps) {
    const { result, color, message, locked, isMe } = useReservableInfo({ key, isActive })

    if (result.isLoading) {
        return <LoadingSpinner />
    }

    if (result.isError) {
        return (
            <IconButton color={'error'} onClick={() => result.refetch()} title={guessErrorMessage(result.error)}>
                <SyncProblem />
            </IconButton>
        )
    }

    return (
        <IconButton color={color} onClick={onClick} title={message}>
            {locked ? <LockIcon /> : <EditIcon />}
        </IconButton>
    )
}

const reservable = z.object({
    Key: string(),
    CurrentEditor: string().nullish(),
    EditingSession: z.coerce.date().nullish(),
    ExtendedLock: z.coerce.date().nullish()
})

function useReservableInfo({ key, isActive }: { key: string; isActive: boolean }) {
    const appContext = useAppContext()
    const result = useQuery({
        queryKey: ['reservation-info', key],
        enabled: Boolean(key),
        queryFn: async () => httpPost(`${ReservableAPI}`, { ReservationKey: key }, reservable)
    })
    const name = useIDToName({
        tableName: 'account',
        ID: result.data?.CurrentEditor
    })

    const editingSessionEnds = result.data?.EditingSession?.toLocaleString()
    const extendedLockEnds = result.data?.ExtendedLock?.toLocaleString()
    const displayName = name || result.data?.CurrentEditor
    const isMe = result.data?.CurrentEditor === appContext?.identity()?.ID
    let color: 'inherit' | 'default' | 'primary' | 'secondary' | 'error' | 'info' | 'success' | 'warning' = 'inherit'
    let message = ''
    let locked = Boolean(extendedLockEnds)

    if (isActive) {
        if (extendedLockEnds) {
            color = 'warning'
            message = `You have an extended lock on this item until ${extendedLockEnds}.`
        } else {
            color = 'success'
            message = `You are currently editing this item.`
        }
    } else if (Boolean(result.data?.CurrentEditor)) {
        if (isMe) {
            color = 'success'
            if (editingSessionEnds) {
                color = 'warning'
                message = `You are currently editing this item in another tab. Session ends at ${editingSessionEnds}.`
            }
            if (extendedLockEnds) {
                message += `You have an extended lock on this item until ${extendedLockEnds}.`
            }
        } else {
            if (extendedLockEnds) {
                color = 'warning'
                message = `This item is currently locked by ${displayName} until ${extendedLockEnds}.`
            } else if (editingSessionEnds) {
                color = 'warning'
                message = `This item is currently being edited by ${displayName} until ${editingSessionEnds}.`
            }
        }
    } else {
        color = 'success'
        message = `You can edit this item.`
        locked = false
    }

    return {
        result,
        color,
        message,
        locked,
        isMe
    }
}
