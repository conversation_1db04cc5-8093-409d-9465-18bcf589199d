import { useEffect, useRef, useCallback } from 'react'
import { atom } from 'jotai/index'
import { useAtom } from 'jotai'
import { httpPatch } from '@/common/client'
import { BASE } from '@/common/constants'
import { ReservableAPI } from '@/pkgs/reservation/requests'

export const SESSION_KEY = 'ie_editing_session'
export const SESSION_DURATION_MS = 10 * 60 * 1000
const RENEW_BEFORE_MS = 3 * 60 * 1000 // renew early to avoid race conditions

function createSessionExpiry(): string {
    const expiry = new Date(Date.now() + SESSION_DURATION_MS)
    return expiry.toISOString()
}

export const activeEditingSessions = atom<number>(0)

export function useEditingSession() {
    const timeoutRef = useRef<ReturnType<typeof setTimeout>>()
    const [activeSessions] = useAtom(activeEditingSessions)

    const renewSessionOnServer = useCallback(
        async (newSession: string): Promise<boolean> => {
            console.log('Renewing. Active sessions count:', activeSessions)

            if (activeSessions === 0) return true

            try {
                console.log('Renewing editing session on server:', newSession)
                await httpPatch(`${ReservableAPI}/session/extend`, {
                    NewSession: newSession
                })
                return true
            } catch (error) {
                console.log(error)
                return false
            }
        },
        [activeSessions]
    )

    useEffect(() => {
        const setupSession = async () => {
            let expiryStr = sessionStorage.getItem(SESSION_KEY)
            let expiry = expiryStr ? new Date(expiryStr) : null
            const now = new Date()

            if (!expiry || expiry <= now) {
                expiry = new Date(now.getTime() + SESSION_DURATION_MS)
                sessionStorage.setItem(SESSION_KEY, expiry.toISOString())
            }

            const timeUntilRenew = expiry.getTime() - now.getTime() - RENEW_BEFORE_MS
            scheduleRenewal(Math.max(timeUntilRenew, 0))
        }

        const scheduleRenewal = (delay: number) => {
            clearTimeout(timeoutRef.current)
            timeoutRef.current = setTimeout(async () => {
                const newExpiry = createSessionExpiry()

                const success = await renewSessionOnServer(newExpiry)
                if (success) {
                    sessionStorage.setItem(SESSION_KEY, newExpiry)
                    scheduleRenewal(SESSION_DURATION_MS - RENEW_BEFORE_MS)
                } else {
                    console.warn('Failed to renew editing session')
                    scheduleRenewal(2000) // Retry in 2 seconds if renewal fails
                }
            }, delay)
        }

        setupSession()

        return () => {
            console.log('Cleaning up editing session')
            clearTimeout(timeoutRef.current)
        }
    }, [renewSessionOnServer])
}
