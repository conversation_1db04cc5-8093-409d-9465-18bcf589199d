import DOMPurify from 'dompurify'
import { videoNodeIframeAttributeName } from '../Embed/VideoNode'
import { domNodeContentFragmentAttrId } from '../ContentFragment/ContentFragmentNode'
import { MONACO_CODE_EMBED_TYPE } from '../MonacoCodeEmbed/MonacoCodeEmbedNode'

const invalidEmptyTags = new Set(['TABLE', 'THEAD', 'TBODY', 'TFOOT', 'TR', 'UL', 'OL', 'DL'])
const inlineTags = new Set([
    'A',
    'ABBR',
    'ACRONYM',
    'B',
    'BR',
    'BUTTON',
    'CITE',
    'EM',
    'I',
    'IMG',
    'SMALL',
    'SPAN',
    'STRONG',
    'SUB',
    'SUP'
])

const blockTags = new Set([
    'ADDRESS',
    'ARTICLE',
    'ASIDE',
    'BLOCKQUOTE',
    'CANVAS',
    'DD',
    'DIV',
    'DL',
    'DT',
    'FIELDSET',
    'FIGCAPTION',
    'FIGURE',
    'FOOTER',
    'FORM',
    'H1',
    'H2',
    'H3',
    'H4',
    'H5',
    'H6',
    'HEADER',
    'HR',
    'LI',
    'MAIN',
    'NAV',
    'NOSCRIPT',
    'OL',
    'P',
    'PRE',
    'SECTION',
    'TABLE',
    'TFOOT',
    'UL',
    'VIDEO'
])

export function sanitizeHTML(htmlString: string) {
    // Child nodes inside iframe/div gets deleted sometimes
    // So, we add inner html to parent node and re-apply "uponSanitizeElement"
    DOMPurify.addHook('afterSanitizeElements', (node: Element) => {
        if ((node.tagName === 'IFRAME' || node.tagName == 'DIV') && !!node.getAttribute('data-tmp-html')) {
            node.innerHTML = node.getAttribute('data-tmp-html')!
            node.removeAttribute('data-tmp-html')
            return
        }

        // Remove specific empty elements
        if (invalidEmptyTags.has(node.nodeName)) {
            if (isNodeEmpty(node)) {
                node.parentNode?.removeChild(node)
                return
            }
        }

        // Wrap inline elements not inside block-level elements
        if (inlineTags.has(node.nodeName)) {
            let parent = node.parentElement

            while (parent && !blockTags.has(parent.nodeName) && parent.nodeName !== 'BODY') {
                parent = parent.parentElement
            }

            if (!parent || (parent && parent.nodeName === 'BODY')) {
                const p = node.ownerDocument.createElement('p')

                node.parentNode?.replaceChild(p, node)
                p.appendChild(node)
            }
        }
    })

    DOMPurify.addHook('uponSanitizeElement', function (currentNode, data, config) {
        if ((currentNode.tagName === 'IFRAME' || currentNode.tagName == 'DIV') && currentNode.innerHTML) {
            currentNode.setAttribute('data-tmp-html', currentNode.innerHTML)
            currentNode.innerHTML = ''
        }
    })

    const sanitizedHTML = DOMPurify.sanitize(htmlString, {
        SAFE_FOR_TEMPLATES: false,
        // USE_PROFILES: { html: true },
        ADD_TAGS: [
            'figure',
            'div',
            'br',
            'ie-fragment',
            'iframe',
            'oembed',
            'a',
            'figure',
            'oembed',
            'a',
            'td',
            'tr',
            'th',
            'h1',
            'h2',
            'h3',
            'h4',
            'h5',
            'h6',
            'ul',
            'li'
        ],
        ALLOW_SELF_CLOSE_IN_ATTR: true,
        ADD_ATTR: [
            'target',
            'style',
            videoNodeIframeAttributeName,
            'data-lexical-youtube',
            'data-lexical-collapsible-content',
            domNodeContentFragmentAttrId,
            MONACO_CODE_EMBED_TYPE,
            'url'
        ]
    })

    DOMPurify.removeAllHooks()
    return sanitizedHTML
}

function isNodeEmpty(node: Element): boolean {
    if (node.children.length > 0) {
        for (const child of node.children) {
            if (!invalidEmptyTags.has(child.nodeName)) {
                return false
            }
            if (!isNodeEmpty(child)) {
                return false
            }
        }
    }

    return true
}
