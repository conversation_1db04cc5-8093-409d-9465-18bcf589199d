
BEGIN;

CREATE TABLE IF NOT EXISTS search_data (
      id uuid NOT NULL DEFAULT uuid_generate_v4()
          constraint pk_search_data primary key,

      ext_id uuid NOT NULL,
      generated_at    TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
      sites uuid[] NOT NULL,

      route text NOT NULL,
      title text NOT NULL,
      keywords text NOT NULL,
      content text NOT NULL,

      type          VARCHAR(150) NOT NULL,
      privacy_level integer NOT NULL,
      tags uuid[] NOT NULL,

      publish_at     TIMESTAMP WITH TIME ZONE NOT NULL ,
      expire_at     TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT '9999-12-31 23:59:59.999999+00'::TIMESTAMP WITH TIME ZONE,

      created_at    TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
      created_by    uuid         NOT NULL,
      updated_at    TIMESTAMP WITH TIME ZONE NOT NULL DEFAULT now(),
      updated_by    uuid         NOT NULL,

      processor_log jsonb NOT NULL DEFAULT '{}'::jsonb,

      row_tsvector tsvector GENERATED ALWAYS AS (
          setweight(to_tsvector('english', coalesce(title, '')), 'A') ||  -- Highest priority
          setweight(to_tsvector('english', coalesce(keywords, '')), 'A') ||  -- Highest priority
          setweight(to_tsvector('english', coalesce(content, '')), 'B') ||  -- Medium priority
          setweight(to_tsvector('english', coalesce(route, '')), 'C') || -- Lowest priority
          setweight(to_tsvector('english', regexp_replace(coalesce(route, ''), '/', ' ', 'g')), 'C') ||   -- Lowest priority
          setweight(to_tsvector('english', regexp_replace(coalesce(route, ''), '[/\-]', ' ', 'g')), 'C')    -- Lowest priority
          ) STORED
);

CREATE INDEX IF NOT EXISTS idx_search_data_tsvector ON search_data USING GIN (row_tsvector);
CREATE INDEX IF NOT EXISTS idx_search_data_sites ON search_data USING GIN (sites);
CREATE INDEX IF NOT EXISTS idx_search_data_tags ON search_data USING GIN (tags);

CREATE INDEX IF NOT EXISTS idx_search_data_publish_expire ON search_data (publish_at, expire_at);
CREATE INDEX IF NOT EXISTS idx_search_data_content_privacy ON search_data (ext_id, privacy_level DESC);
CREATE INDEX IF NOT EXISTS idx_search_data_generated_at ON search_data (generated_at);
CREATE INDEX IF NOT EXISTS idx_search_data_created_at ON search_data (created_at);
CREATE INDEX IF NOT EXISTS idx_search_data_created_by ON search_data (created_by);
CREATE INDEX IF NOT EXISTS idx_search_data_updated_at ON search_data (updated_at);
CREATE INDEX IF NOT EXISTS idx_search_data_updated_by ON search_data (updated_by);

GRANT ALL ON TABLE search_data to contentmanager_application_user;



CREATE INDEX IF NOT EXISTS idx_content_updated ON content (updated);

COMMIT;
