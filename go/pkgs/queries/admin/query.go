package admin

import (
	"contentmanager/infrastructure/database/driver"
	"contentmanager/infrastructure/database/pgxx"
	"contentmanager/library/shared"
	"contentmanager/library/shared/pagination"
	"contentmanager/library/shared/pagx"
	"contentmanager/library/shared/result"
	"contentmanager/library/shared/sortx"
	"contentmanager/library/utils/slicexx"
	"contentmanager/pkgs/auth"
	"contentmanager/pkgs/queries/models"
	"errors"
	"fmt"
	uuid "github.com/satori/go.uuid"
	"gorm.io/datatypes"
)

type (
	SearchQuery struct {
		pagx.Query
		sortx.SortingQuery
		Search      string
		StructureID *uuid.UUID `binding:"omitempty,cm_uuid"`
		Inactive    bool
		ContentType string
	}

	UpdateQueryDTO struct {
		auth.TenantWideBase

		Data        datatypes.JSONType[models.QueryData]
		Title       string
		Description string
	}

	CreateQueryDTO struct {
		UpdateQueryDTO

		// Immutable
		ContentTypes []string
		StructureID  *uuid.UUID
	}
)

func (q UpdateQueryDTO) GetScopeEntity() string {
	return "cm.query"
}

func SearchQueries(r *shared.AppContext, q SearchQuery) result.Result[pagx.Paginated[models.Query]] {
	var pag pagx.Paginated[models.Query]

	tx := r.TenantDatabase().Where("active = ?", !q.Inactive)

	if len(q.ContentType) > 0 {
		tx = tx.Where(pgxx.ArrayHasAny("content_types", driver.PgStringArray{q.ContentType}))
	}
	if q.StructureID != nil {
		tx = tx.Where("structure_id = ?", *q.StructureID)
	}
	if q.Search != "" {
		tx = tx.Where(models.Query{}.SearchQuery(), q.Search)
	}

	tx = tx.Order(q.GetSortingSQL("title"))

	if err := pagination.Paginate(tx, q.Query, &pag); err != nil {
		return result.Error(err, pag)
	}
	return result.Success(pag)
}

func CreateQuery(r *shared.AppContext, dto CreateQueryDTO) result.Result[uuid.UUID] {
	if err := uniqueByTitle(r, dto.Title, uuid.Nil); err != nil {
		return result.Error(err, uuid.Nil)
	}

	query := models.Query{
		Data:         dto.Data,
		Title:        dto.Title,
		Description:  dto.Description,
		ContentTypes: dto.ContentTypes,
		StructureID:  dto.StructureID,
		Active:       true,
	}

	query.Track(r.AppTime().NowUTC(), r.Account().ID)

	if err := r.TenantDatabase().Table("queries").Create(&query).Error; err != nil {
		return result.Error(err, uuid.Nil)
	}

	return result.Success(query.ID)
}

func UpdateQuery(r *shared.AppContext, id uuid.UUID, dto UpdateQueryDTO) result.EmptyResult {
	if err := uniqueByTitle(r, dto.Title, id); err != nil {
		return result.ErrorEmpty(err)
	}

	var query models.Query
	if err := r.TenantDatabase().Where("id = ?", id).First(&query).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	query.Title = dto.Title
	query.Description = dto.Description
	query.Data = dto.Data

	query.Track(r.AppTime().NowUTC(), r.Account().ID)

	if err := r.TenantDatabase().Save(&query).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	return result.SuccessEmpty()
}

func SetQueryActive(r *shared.AppContext, id uuid.UUID, active bool) result.EmptyResult {
	var query models.Query
	if err := r.TenantDatabase().Where("id = ?", id).First(&query).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	query.Active = active
	if !active {
		query.Title = query.Title + " (deleted) " + r.AppTime().NowUTC().Format("2006-01-02 15:04:05")
	}

	query.Track(r.AppTime().NowUTC(), r.Account().ID)

	if err := r.TenantDatabase().Save(&query).Error; err != nil {
		return result.ErrorEmpty(err)
	}

	return result.SuccessEmpty()
}

func uniqueByTitle(r *shared.AppContext, title string, id uuid.UUID) error {
	type Query struct {
		ID    uuid.UUID
		Title string
	}

	var queries []Query
	if err := r.TenantDatabase().Table(models.Query{}.TableName()).
		Where("active").
		Where("lower(title) = lower(?)", title).
		Find(&queries).Error; err != nil {
		return err
	}

	if len(queries) == 0 {
		return nil
	}
	if len(queries) == 1 && queries[0].ID == id {
		return nil
	}

	conflicts := slicexx.Filter(queries, func(c Query) bool {
		return c.ID != id
	})
	conflictNames := slicexx.Select(conflicts, func(c Query) string {
		return fmt.Sprintf("%s (%s)", c.Title, c.ID.String())
	})
	return errors.New(fmt.Sprintf("The query title is already taken. Conflicting queries: %s", slicexx.JoinAsStrings(conflictNames, ", ")))
}

var _ auth.Secured = (*UpdateQueryDTO)(nil)
