package handlebars

import (
	"contentmanager/library/tenant/public/utils/handlebars"
	"contentmanager/library/utils/slicexx/jsonxx"
	"contentmanager/pkgs/queries/models"
	"contentmanager/pkgs/queries/templates"
)

func init() {
	handlebars.RegisterHelper("queryColumns", queryColumnsHelper)
}

func queryColumnsHelper(options *handlebars.Options) handlebars.SafeString {
	root := options.RootCtx()
	vm, ok := root.(templates.CompileViewModel)
	if !ok {
		return handlebars.SafeString("<!-- [queryColumnsHelper] Error: context is not a CompileViewModel -->")
	}
	var cells []models.TableCell
	if err := jsonxx.UnmarshalJSONAtPath(vm.Template.Data, "Cells", &cells); err != nil {
		return handlebars.SafeString("<!-- [queryColumnsHelper] Error: " + err.Error() + " -->")
	}

	if len(cells) == 0 {
		return handlebars.SafeString("<!-- [queryColumnsHelper] Error: No cells provided for table view! -->")
	}

	result := ""
	for i, item := range cells {
		// computes private data
		data := options.NewIterDataFrame(len(cells), i, nil)

		// evaluates block
		block := options.EvalBlock(item, data, i)
		result += block
	}
	return handlebars.SafeString(result)
}
