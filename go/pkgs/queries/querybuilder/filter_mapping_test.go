package querybuilder

import (
	"fmt"
	"os"
	"reflect"
	"regexp"
	"runtime"
	"strings"
	"testing"
)

func TestFilterComponentsMappingSync(t *testing.T) {
	// Get Go mapping structure with function names
	goMapping := getGoMapping()

	// Read and parse TypeScript file
	tsContent, err := os.ReadFile("../../../../react/src/pkgs/queries/filterComponentsMapping.ts")
	if err != nil {
		t.Fatalf("Failed to read TypeScript file: %v", err)
	}

	// Extract the mapping object from TypeScript content
	tsMapping, err := extractTSMapping(string(tsContent))
	if err != nil {
		t.Fatalf("Failed to parse TypeScript mapping: %v", err)
	}

	// Compare mappings
	for fieldType, goOps := range goMapping {
		filterOperation, exists := tsMapping[fieldType]
		if !exists {
			t.Errorf("Filter type %q exists in Go but not in TypeScript", fieldType)
			continue
		}

		for opName, goFn := range goOps {
			tsFn, exists := filterOperation[opName]
			if !exists {
				t.<PERSON><PERSON><PERSON>("Operation %q for filter type %q exists in Go but not in TypeScript",
					opName, fieldType)
				continue
			}

			// Compare function names
			if goFn != tsFn {
				t.Errorf("Function mismatch for filter type %q operation %q:\nGo: %v\nTS: %v",
					fieldType, opName, goFn, tsFn)
			}
		}
	}

	// Check for TypeScript operations that don't exist in Go
	for operation, tsOps := range tsMapping {
		goOps, exists := goMapping[operation]
		if !exists {
			t.Errorf("Filter type %q exists in TypeScript but not in Go", operation)
			continue
		}

		for opName := range tsOps {
			if _, exists := goOps[opName]; !exists {
				t.Errorf("Operation %q for filter type %q exists in TypeScript but not in Go",
					opName, operation)
			}
		}
	}
}

func getFunctionName(i interface{}) string {
	// Get the full function name including package path
	fullName := runtime.FuncForPC(reflect.ValueOf(i).Pointer()).Name()

	// Extract just the function name
	parts := strings.Split(fullName, ".")
	return parts[len(parts)-1]
}

func extractTSMapping(content string) (map[string]map[string]string, error) {
	// get the body of filterComponentsMapping object
	re := regexp.MustCompile(`(?s)export const filterComponentsMapping[^=]+=\s*({.*?})[\s]*as const`)
	matches := re.FindStringSubmatch(content)
	if len(matches) < 2 {
		return nil, fmt.Errorf("could not find mapping object in TypeScript file")
	}

	// Extract the object string
	objStr := matches[1]

	result := make(map[string]map[string]string)
	var currentType string

	// Split into lines and process each line
	lines := strings.Split(objStr, "\n")
	for i := 0; i < len(lines); i++ {
		line := strings.TrimSpace(lines[i])

		// Skip empty lines
		if line == "" {
			continue
		}

		// Check if this is a new type definition
		if strings.HasSuffix(line, "{") {
			typeMatch := regexp.MustCompile(`'?([\w-]+)'?:\s*{`).FindStringSubmatch(line)
			if typeMatch != nil {
				currentType = typeMatch[1]
				result[currentType] = make(map[string]string)
			}
			continue
		}

		// Extract operation and component
		opMatch := regexp.MustCompile(`(\w+):\s*(\w+),?`).FindStringSubmatch(line)
		if opMatch != nil {
			operation := opMatch[1]
			componentName := opMatch[2]
			result[currentType][operation] = componentName
		}
	}

	return result, nil
}

func getGoMapping() map[string]map[string]string {
	goMapping := make(map[string]map[string]string)
	for operation, operations := range filterComponentsMapping {
		goMapping[operation] = make(map[string]string)
		for opName, fn := range operations {
			// Get function name from the function pointer
			fnName := getFunctionName(fn)
			goMapping[operation][opName] = fnName
		}
	}
	return goMapping
}
