package matchredirect

import (
	"contentmanager/logging"
	"contentmanager/pkgs/multitenancy"
	"context"
	uuid "github.com/satori/go.uuid"
	"net/http"
	"net/url"
	"time"
)

const REFRESH_INTERVAL = 15 * time.Minute

type (
	RedirectMatcher interface {
		Match(tenantID uuid.UUID, r *http.Request) (*url.URL, bool)
	}

	redirectMatcher struct {
		mta          multitenancy.Accessor
		tenantsRules multitenancy.SnapshotCache[uuid.UUID, []Rule]
	}
)

func Initialize(mta multitenancy.Accessor) RedirectMatcher {
	instance := &redirectMatcher{
		mta:          mta,
		tenantsRules: multitenancy.NewSnapshotCache[uuid.UUID, []Rule](map[uuid.UUID][]Rule{}),
	}

	go func() {
		instance.refresh()
		for range time.Tick(REFRESH_INTERVAL) {
			instance.refresh()
		}
	}()

	return instance
}

// Match processes `rules` []Rule in order and the first match win, so `rules` should be in desired order
// (in our case in DB we store them with `priority int` column and order them by it while querying).
func (m *redirectMatcher) Match(tenantID uuid.UUID, r *http.Request) (*url.URL, bool) {
	u := &url.URL{
		Scheme: "https",
		Host:   r.Host,
		Path:   r.URL.Path,
	}

	rules, ok := m.tenantsRules.Get(tenantID)
	if !ok {
		return u, false
	}

	if !u.IsAbs() {
		return u, false
	}
	if len(rules) == 0 {
		return u, false
	}

	return match(u, rules)
}

func match(u *url.URL, rules []Rule) (*url.URL, bool) {
	full, path := getUrlParts(u)
	for _, r := range rules {
		strToCheck := path
		if r.UrlContext == HostAndPath {
			strToCheck = full
		}

		switch r.Type {
		case String:
			if res, ok := checkStringPattern(strToCheck, r); ok {
				return res, true
			}
		case Regex, Custom:
			if res, ok := checkRegexpPattern(strToCheck, r); ok {
				return res, true
			}
		}
	}

	return u, false
}

func (m *redirectMatcher) refresh() {
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()

	now := time.Now()
	tenants := m.mta.Tenants()
	refreshed := make(map[uuid.UUID][]Rule, 1000)

	for _, tenant := range tenants {
		db, errorDB := m.mta.TenantDBOrError(tenant.ID)
		if errorDB != nil {
			logging.RootLogger().Error().Err(errorDB).Str("tenant_id", tenant.ID.String()).Msg("[redirectMatcher] Failed to get tenant database")
			continue
		}

		var rules []Rule
		if err := db.WithContext(ctx).Table("redirect_rule").
			Where("active").Order("priority asc").
			Find(&rules).Error; err != nil {
			logging.RootLogger().Error().Err(err).Str("tenant_id", tenant.ID.String()).Msg("[redirectMatcher] Failed to fetch rules")
			continue
		}

		for i := range rules {
			if err := rules[i].Initialize(); err != nil {
				logging.RootLogger().Error().Err(err).Str("tenant_id", tenant.ID.String()).Msg("[redirectMatcher] Failed to compile rule")
				continue
			}
		}

		refreshed[tenant.ID] = rules
	}

	m.tenantsRules.Refresh(refreshed)
	logging.RootLogger().Info().Dur("duration", time.Since(now)).Msg("[redirectMatcher] Redirects refreshed")
}
