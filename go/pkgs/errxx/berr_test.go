package errxx

import (
	"errors"
	"testing"
)

func Test_Is(t *testing.T) {
	err := NewPermissionError("delete post")

	if !errors.Is(err, ErrPermissionDenied) {
		t.<PERSON>("expected PermissionDenied")
	}

	if !errors.Is(err, &PermissionError{}) {
		t.<PERSON>("expected error to be of type PermissionError")
	}

}

func Test_Data(t *testing.T) {
	err := New("test error", "some data")
	if err.Data() != "some data" {
		t.<PERSON>("expected data to be 'some data', got %v", err.Data())
	}

	var err2 error = err

	if x, ok := err2.(interface{ Data() any }); !ok {
		t.<PERSON>("expected error to have Data method")
	} else if x.Data() != "some data" {
		t.Errorf("expected data to be 'some data', got %v", x.Data())
	}
}
