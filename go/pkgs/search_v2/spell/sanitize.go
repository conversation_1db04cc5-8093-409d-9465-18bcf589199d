package spell

import (
	"errors"
	"regexp"
	"strings"
	"unicode"
)

var (
	ErrNonEnglishText = errors.New("text appears to be non-English")

	// Pattern for all punctuation except apostrophes
	punctuationRegex = regexp.MustCompile(`[^\p{L}\p{N}'’]+`)

	// Pattern for different types of apostrophes
	apostropheRegex = regexp.MustCompile(`['’]`)
)

// isEnglish performs a basic check if the text appears to be in English
// by checking the ratio of ASCII letters to total text length
func isEnglish(text string) bool {
	if len(text) == 0 {
		return true
	}

	for _, r := range text {
		if unicode.IsLetter(r) {
			if r > 127 { // ASCII range
				return false
			}
		}
	}
	return true
}

// cleanText normalizes apostrophes and removes other punctuation
func cleanText(text string) string {
	// First normalize all apostrophe types to a single type
	text = apostropheRegex.ReplaceAllString(text, "'")

	// Remove all other punctuation
	text = strings.TrimSpace(punctuationRegex.ReplaceAllString(text, " "))

	return text
}

func Sanitize(input string) (string, error) {
	cleaned := cleanText(input)

	// Check if the text appears to be English
	if !isEnglish(cleaned) {
		return "", ErrNonEnglishText
	}

	lower := strings.ToLower(cleaned)
	return lower, nil
}
