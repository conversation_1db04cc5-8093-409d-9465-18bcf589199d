package admin

import (
	middlewares2 "contentmanager/infrastructure/middlewares"
	"contentmanager/infrastructure/middlewares/bindauth"
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	"contentmanager/library/utils"
	"contentmanager/pkgs/multitenancy"
	"contentmanager/pkgs/reservation/gormxx"
	"contentmanager/pkgs/reservation/models"
	"errors"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
	"net/http"
	"time"
)

func init() {
	// Register the reservation plugin
	multitenancy.RegisterPlugin(gormxx.ReservationPlugin{})
}

type (
	BaseRequiredFields struct {
		ID        uuid.UUID
		Workspace string
		Table     string
	}
)

func (b BaseRequiredFields) Validate() bool {
	return b.Table != "" && b.ID != uuid.Nil
}

func AddAdminReservations(r *httpService.DefaultMiddleware) *httpService.DefaultMiddleware {

	r.Group("/api/v1/reservable", func(router httpService.Router) {

		router.Post("", func(w http.ResponseWriter, r *shared.AppContext, props struct {
			bindauth.BindableParams
			FromBody struct {
				ReservationKey string
			}
		}) {
			var reservation models.Reservation
			if err := r.TenantDatabase().Where("key = ?", props.FromBody.ReservationKey).First(&reservation).Error; err != nil {
				if !errors.Is(err, gorm.ErrRecordNotFound) {
					http.Error(w, "not found", http.StatusNotFound)
					return
				}
				reservation.Key = props.FromBody.ReservationKey
			}

			utils.WriteResponseJSON(w, reservation, nil)
		})

		router.Patch("/session/extend", func(w http.ResponseWriter, r *shared.AppContext, props struct {
			bindauth.BindableParams
			FromBody struct {
				NewSession time.Time
			}
		}) {
			utils.WriteResultJSON(w, ExtendEditingSession(r, props.FromBody.NewSession))
		})

		router.Post("/session/start", func(w http.ResponseWriter, r *shared.AppContext, props struct {
			bindauth.BindableParams
			FromBody struct {
				ReservationKey string
			}
		}) {
			utils.WriteResultJSON(w, StartEditingSessionFor(r, props.FromBody.ReservationKey))
		})

		router.Post("/session/end", func(w http.ResponseWriter, r *shared.AppContext, props struct {
			bindauth.BindableParams
			FromBody struct {
				ReservationKey string
			}
		}) {
			utils.WriteResultJSON(w, EndEditingSessionFor(r, props.FromBody.ReservationKey))
		})

		//router.Put("", func(w http.ResponseWriter, r *shared.AppContext, p struct {
		//	bindauth.BindableParams
		//	FromBody Params
		//}) {
		//	if !p.FromBody.Validate() {
		//		http.Error(w, "missing required table & entityID", http.StatusBadRequest)
		//		return
		//	}
		//	if p.FromBody.NextEditingSession == nil {
		//		http.Error(w, "bad request", http.StatusBadRequest)
		//		return
		//	}
		//	writeResult(w, UpdateEditingSession(r, p.FromBody))
		//})
		//
		//router.Delete("", func(w http.ResponseWriter, r *shared.AppContext, p struct {
		//	bindauth.BindableParams
		//	FromQuery Params
		//}) {
		//	if !p.FromQuery.Validate() {
		//		http.Error(w, "missing required table & entityID", http.StatusBadRequest)
		//		return
		//	}
		//	if p.FromQuery.EditingSession == nil {
		//		http.Error(w, "bad request", http.StatusBadRequest)
		//		return
		//	}
		//	writeResult(w, EndEditingSession(r, p.FromQuery))
		//})
		//
		//router.Post("", func(w http.ResponseWriter, r *shared.AppContext, p struct {
		//	bindauth.BindableParams
		//	FromQuery Params
		//}) {
		//	if !p.FromQuery.Validate() {
		//		http.Error(w, "missing required table & entityID", http.StatusBadRequest)
		//		return
		//	}
		//	if p.FromQuery.EditingSession == nil {
		//		http.Error(w, "bad request", http.StatusBadRequest)
		//		return
		//	}
		//	writeResult(w, EndEditingSession(r, p.FromQuery))
		//})
		//
		//router.Delete("/lock", func(w http.ResponseWriter, r *shared.AppContext, p struct {
		//	bindauth.BindableParams
		//	FromQuery Params
		//}) {
		//	if !p.FromQuery.Validate() {
		//		http.Error(w, "missing required table & entityID", http.StatusBadRequest)
		//		return
		//	}
		//	// Doesn't currently require an editing session, as you can remove an extended-lock without one as long as you are the current_editor
		//	writeResult(w, EndExtendedLock(r, p.FromQuery))
		//})
		//
		//router.Delete("/lock/override", func(w http.ResponseWriter, r *shared.AppContext, p struct {
		//	bindauth.BindableParams
		//	FromQuery BaseRequiredFields
		//}) {
		//	if !p.FromQuery.Validate() {
		//		http.Error(w, "missing required table & entityID", http.StatusBadRequest)
		//		return
		//	}
		//	writeResult(w, OverrideExtendedLock(r, p.FromQuery))
		//
		//})

	}, middlewares2.RequiresAuthenticationMiddleware(), bindauth.BindParamsMiddleware())

	return r
}
