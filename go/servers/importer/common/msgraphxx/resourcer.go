package msgraphxx

import (
	"contentmanager/pkgs/msgraph"
)

type Resourcer struct {
	client *msgraph.GraphClient
}

func NewResourcer(client *msgraph.GraphClient) *Resourcer {
	return &Resourcer{client: client}
}
func (r Resourcer) ListMailboxCalendars(mailboxId string) (msgraph.Calendars, error) {
	return msgraph.GetCalendars(r.client, mailboxId)
}
func (r Resourcer) ListCalendarEvents(calendars msgraph.Calendars) (msgraph.CalendarEvents, error) {
	return msgraph.GetEventsFromCalendars(calendars)
}
