package adminServices

import (
	"contentmanager/library/shared"
	"contentmanager/library/tenant/admin/dataaccess"
	"contentmanager/library/tenant/common/models"
	uuid "github.com/satori/go.uuid"
	"gorm.io/gorm"
)

func GetBusStatusResults(dbCon *gorm.DB, siteId uuid.NullUUID, useSiteId bool, ignoreOnTime bool) ([]commonModels.BusStatus, error) {
	statusChain, err := adminDataaccess.GetBusStatusData(dbCon, siteId, useSiteId, ignoreOnTime)
	if err != nil {
		return []commonModels.BusStatus{}, err
	}

	return statusChain, err
}
func GetAllBusStatusResults(dbCon *gorm.DB, siteId uuid.NullUUID) ([]commonModels.BusStatus, error) {
	statusChain, err := adminDataaccess.GetAllBusStatusData(dbCon, siteId)
	if err != nil {
		return []commonModels.BusStatus{}, err
	}

	return statusChain, err
}
func CreateBusStatus(r *shared.AppContext, queryModel commonModels.BusStatus) (commonModels.BusStatus, error) {
	queryModel.Active = true
	return adminDataaccess.CreateBusStatus(r, queryModel)
}
func UpdateBusStatus(dbCon *gorm.DB, queryModel commonModels.BusStatus) (commonModels.BusStatus, error) {
	if status, err := adminDataaccess.UpdateBusStatus(dbCon, queryModel); err == nil {
		return status, nil
	} else {
		return status, err
	}
}
func DeleteBusStatus(dbCon *gorm.DB, queryModel commonModels.BusStatus) (commonModels.BusStatus, error) {
	if status, err := adminDataaccess.DeleteBusStatus(dbCon, queryModel); err == nil {
		return status, nil
	} else {
		return status, err
	}
}
