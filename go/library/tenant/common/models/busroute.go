package commonModels

import (
	dbDriver "contentmanager/infrastructure/database/driver"
	"contentmanager/library/utils/slicexx"
	"contentmanager/pkgs/auth"
	uuid "github.com/satori/go.uuid"
)

type BusRoute struct {
	auth.TenantWideBase
	ID     uuid.UUID            `json:"id" gorm:"column:id;type:uuid;primary_key;not null;default:uuid_generate_v4()"`
	Name   string               `json:"name" gorm:"column:name;type:character varying(64);not null"`
	Areas  dbDriver.PgUUIDArray `json:"areas" gorm:"column:areas;type:uuid[]"`
	Sites  dbDriver.PgUUIDArray `json:"sites" gorm:"column:sites;type:uuid[]"`
	Active bool                 `json:"active" gorm:"column:active;type:boolean; not null;default:true"`
}

func (r BusRoute) Validate() bool {
	if len(r.Sites) == 0 || len(r.Areas) == 0 || len(r.Name) == 0 {
		return false
	}
	return len(slicexx.Filter[uuid.UUID](r.Areas, func(id uuid.UUID) bool {
		return id != uuid.Nil
	})) > 0
}

func (r BusRoute) SelectNameQuery() string {
	return "name"
}

func (BusRoute) TableName() string {
	return "bus_route"
}
func (BusRoute) GetScopeEntity() string {
	return "cm.transportation.bus_route"
}
