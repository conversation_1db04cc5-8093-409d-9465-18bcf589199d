package bindauth

import (
	"contentmanager/library/httpService"
	"contentmanager/library/shared"
	commonModels "contentmanager/library/tenant/common/models"
	"contentmanager/library/utils"
	"contentmanager/pkgs/auth"
	"contentmanager/pkgs/auth/identity"
	"contentmanager/tests"
	"encoding/json"
	uuid "github.com/satori/go.uuid"
	"net/http"
	"net/http/httptest"
	"os"
	"strings"
	"testing"
)

func Test_ExtractKeys(t *testing.T) {
	cases := []struct {
		input  string
		output map[string]interface{}
	}{
		{
			input:  "id = @FromPath.ID",
			output: map[string]interface{}{"FromPath.ID": struct{}{}},
		},
		{
			input:  "id = @FromPath.ID AND workspace = @FromPath.Workspace",
			output: map[string]interface{}{"FromPath.ID": struct{}{}, "FromPath.Workspace": struct{}{}},
		},
		{
			input:  "id = @FromPath.ID AND name = @FromPath.Name AND id = @FromPath.ID",
			output: map[string]interface{}{"FromPath.ID": struct{}{}, "FromPath.Name": struct{}{}},
		},
	}
	for _, test := range cases {
		t.Run(test.input, func(t *testing.T) {
			result := extractKeys(test.input)
			if len(result) != len(test.output) {
				t.Errorf("Expected %v keys, got %v", len(test.output), len(result))
				return
			}
			for k := range test.output {
				if _, ok := result[k]; !ok {
					t.Errorf("Expected key %v, but not found", k)
				}
			}
		})
	}
}

func Test_Server(t *testing.T) {
	os.Setenv("DEBUG", "1")

	ctx := tests.InitLogging("test")
	db, dispose := tests.InitTenantDB()
	defer dispose()

	if err := db.Save(&commonModels.BusArea{ID: uuid.FromStringOrNil("cf7c279b-d6a8-4d73-a192-d37d019087e7"), Name: "issue", Active: true}).Error; err != nil {
		t.Fatalf("Failed to save bus area: %s", err)
	}

	defaultMiddleware := httpService.DefaultLight()
	defaultMiddleware.Use(func(rw http.ResponseWriter, r *http.Request, c httpService.Context) {
		shCtx := shared.NewMockAppContextBasic(shared.MockAppContextBasicParams{
			TenantDB: db.WithContext(ctx),
			Identity: &identity.Account{IsAdmin: true},
			Request:  r,
		})
		c.Map(shCtx)
	})

	defaultMiddleware.Group("/binding", func(r httpService.Router) {
		r.Get("/test/:id", func(w http.ResponseWriter, params struct {
			BindableParams
			FromPath struct {
				ID uuid.UUID
			}
			FromDB commonModels.BusArea `query:"id = @FromPath.ID"`
		}) {
			utils.WriteResponseJSON(w, params, nil)
		})

		r.Post("/test/:id", func(w http.ResponseWriter, params struct {
			BindableParams
			FromPath struct {
				ID uuid.UUID
			}
			FromQuery TQ
			FromBody  IDName
		}) {
			utils.WriteResponseJSON(w, params, nil)
		})

	}, BindParamsMiddleware())

	// validate routes
	ValidateRoutes(defaultMiddleware)
	// Create a test server using httptest
	ts := httptest.NewServer(defaultMiddleware)
	defer ts.Close() // Make sure to close the server when the test finishes

	if resp, err := http.Post(ts.URL+"/binding/test/cf7c279b-d6a8-4d73-a192-d37d019087e7?page=1&pageSize=10&Events[]=send_issue_stopped&Events[]=send_issue_started&SomeObject[]=%7B%22ID%22:%225f9b1b0b-8c1a-4b1c-9c1a-4b1c9c1a4b1c%22,%22Name%22:%22issue%22%7D&SomeObject[]=%7B%22ID%22:%225f9b1b0b-8c1a-4b1c-9c1a-4b1c9c1a4b1c%22,%22Name%22:%22issue%22%7D&siteId=00ffecc6-d8fb-46c7-b357-462bd4d7ac6d",
		"application/json",
		strings.NewReader(`{"ID":"117c279b-d6a8-4d73-a192-d37d019087e7","Name":"issue"}`)); err != nil {
		t.Fatalf("Failed to send request: %s", err)
	} else {
		m := asMap(resp)
		if m["FromPath"].(map[string]interface{})["ID"].(string) != "cf7c279b-d6a8-4d73-a192-d37d019087e7" {
			t.Errorf("Expected FromPath.ID to be cf7c279b-d6a8-4d73-a192-d37d019087e7, got %v", m["FromPath"].(map[string]interface{})["ID"])
		}
		if m["FromQuery"].(map[string]interface{})["PageSize"].(float64) != 10 {
			t.Errorf("Expected FromQuery.PageSize to be 10, got %v", m["FromQuery"].(map[string]interface{})["PageSize"])
		}
		if m["FromBody"].(map[string]interface{})["ID"].(string) != "117c279b-d6a8-4d73-a192-d37d019087e7" {
			t.Errorf("Expected FromBody.ID to be 117c279b-d6a8-4d73-a192-d37d019087e7, got %v", m["FromBody"].(map[string]interface{})["ID"])
		}
	}

	if resp, err := http.Get(ts.URL + "/binding/test/cf7c279b-d6a8-4d73-a192-d37d019087e7"); err != nil {
		t.Fatalf("Failed to send request: %s", err)
	} else {
		m := asMap(resp)
		if m["FromPath"].(map[string]interface{})["ID"].(string) != "cf7c279b-d6a8-4d73-a192-d37d019087e7" {
			t.Errorf("Expected FromPath.ID to be cf7c279b-d6a8-4d73-a192-d37d019087e7, got %v", m["FromPath"].(map[string]interface{})["ID"])
		}
	}
}

func Test_ShouldPanicIfParamsIsNotTheLast(t *testing.T) {
	os.Setenv("DEBUG", "1")

	defaultMiddleware := httpService.DefaultLight() // Or however you set up your router

	defaultMiddleware.Group("/binding", func(r httpService.Router) {
		r.Get("/test/:id", func(params struct { // params should be the last argument
			BindableParams
			FromPath struct {
				ID uuid.UUID
			}
		}, w http.ResponseWriter) {
			utils.WriteResponseJSON(w, params, nil)
		})

	}, BindParamsMiddleware())

	if err := validateRoutes(defaultMiddleware); err == nil {
		t.Errorf("Expected error, but got nothing. ")
	}
}

func Test_ShouldPanicIfWrongFieldName(t *testing.T) {
	os.Setenv("DEBUG", "1")

	defaultMiddleware := httpService.DefaultLight() // Or however you set up your router

	defaultMiddleware.Group("/binding", func(r httpService.Router) {
		r.Get("/test/:id", func(params struct { // params should be the last argument
			BindableParams
			RandomName string // should panic because the field name is not valid
			FromPath   struct {
				ID uuid.UUID
			}
		}, w http.ResponseWriter) {
			utils.WriteResponseJSON(w, params, nil)
		})

	}, BindParamsMiddleware())

	if err := validateRoutes(defaultMiddleware); err == nil {
		t.Errorf("Expected error, but got nothing. ")
	}
}

func Test_ShouldPanicIfFromDBIsNotTheTabler(t *testing.T) {
	os.Setenv("DEBUG", "1")

	defaultMiddleware := httpService.DefaultLight() // Or however you set up your router

	defaultMiddleware.Group("/binding", func(r httpService.Router) {
		r.Get("/test/:id", func(w http.ResponseWriter, params struct {
			BindableParams
			FromDB struct {
				ID uuid.UUID
			}
		}) {
			utils.WriteResponseJSON(w, params, nil)
		})

	}, BindParamsMiddleware())

	if err := validateRoutes(defaultMiddleware); err == nil {
		t.Errorf("Expected error, but got nothing. ")
	} else if !strings.Contains(err.Error(), "schema.Tabler") {
		t.Errorf("Expected error to contain schema.Tabler, but got %s", err.Error())
	}
}

type (
	IDName struct {
		ID   uuid.UUID
		Name string
	}
	TQ struct {
		auth.TenantWideBase
		PageSize   int
		Page       int
		Events     []string `json:"Events[]"`
		SomeObject []IDName `json:"SomeObject[]"`
	}
)

func asMap(r *http.Response) map[string]interface{} {
	var m map[string]interface{}
	if err := json.NewDecoder(r.Body).Decode(&m); err != nil {
		panic(err)
	}
	return m
}
